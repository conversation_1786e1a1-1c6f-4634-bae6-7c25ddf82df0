<script setup lang="ts">
// 方案互动
import { message, Modal } from 'ant-design-vue';
import { onMounted, onBeforeUnmount, ref, reactive, computed, nextTick, inject } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { debounce } from 'lodash';

import {
  saveDataBy,
  getDataBy,
  delData,
  errorModal,
  resolveParam,
  routerParam,
  numComputedArrMethod,
  meetingProcessOrchestration,
} from '@haierbusiness-front/utils';
import { schemeApi, miceBidManOrderListApi } from '@haierbusiness-front/apis';
import { miceSchemeSubmitRequest, ProcessOrchestrationServiceTypeEnum } from '@haierbusiness-front/common-libs';

import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
const { loginUser } = storeToRefs(applicationStore());

import schemeInfo from './schemeComponent/schemeInfo.vue';
import schemeHotel from './schemeComponent/schemeHotel.vue';
import schemePlan from './schemeComponent/schemePlan.vue';
import schemeMaterial from './schemeComponent/schemeMaterial.vue';
import schemePresents from './schemeComponent/schemePresents.vue';
import schemeOther from './schemeComponent/schemeOther.vue';
import schemeServiceFee from './schemeComponent/schemeServiceFee.vue';
import schemeFiles from './schemeComponent/schemeFiles.vue';
import schemeTotal from './schemeComponent/schemeTotal.vue';

const route = useRoute();
const router = useRouter();

const isCloseLastTab = inject<Ref<boolean>>('isCloseLastTab');

const schemeContainerRef = ref();
const schemePlanRef = ref(null);
const schemeMaterialRef = ref(null);
const schemePresentRef = ref(null);
const schemeOtherRef = ref(null);
const schemeFeeRef = ref(null);
const schemeFileRef = ref(null);

const autoSave = ref(null); // 自动保存
const countdownTimer = ref(null); //
const countdownTime = ref<number>(60);
const cacheLoading = ref<Boolean>(false);
const spinLoading = ref<Boolean>(false);
const processLoading = ref<Boolean>(false);
const schemeLoading = ref<Boolean>(false);
const isSchemeCache = ref<Boolean>(false);
const subLoading = ref(false); // 完成提报

const abandonShow = ref<boolean>(false);
const abandonReason = ref<string>('');
const schemeAbandonReason = ref<string>(''); // 驳回内容反显

const hotelList = ref<array>([]); // 酒店
const schemePlanObj = ref<miceSchemeSubmitRequest>({}); // 每日计划
const schemeMaterialObj = ref<miceSchemeSubmitRequest>({}); // 布展物料
const schemePresentArr = ref<array>([]); // 礼品
const schemeOtherArr = ref<array>([]); // 其他
const schemeFeeObj = ref<miceSchemeSubmitRequest>({}); // 全单服务费
const schemeFileObj = ref<array>([]); // 附件

const planPrice = ref<number>(0); // 每日计划 - 金额
const planEachPriceList = ref<array>([]); // 每日计划 - 金额
const materialPrice = ref<number>(0); // 布展物料 - 金额
const presentPrice = ref<number>(0); // 礼品 - 金额
const otherPrice = ref<number>(0); // 其他 - 金额
const totalPrice = ref<number>(0); // 全单服务费方案 - 总金额

const miceId = ref<number>(null);
const miceSchemeId = ref<number>(null);
const schemeType = ref<string>(''); // 方案提报类型 // 查看需求-view / 未提报-notReported / 已提报-reported / 查看方案-schemeView / 待竞价 - notBidding / 竞价完成 - biddingView / 账单上传 - billUpload
const hotelLockId = ref<string>('');
const miceSchemeDemandHotelLockId = ref<string>('');

const merchantId = ref<number>(null); // 服务商Id
const merchantType = ref<number>(null); // 服务商类型

const demandDetail = ref<any>({}); // 需求详情
const schemeDetail = ref<any>({}); // 方案详情

const schemeTotalInfo = ref<any>({}); // 合计详情

const processNode = ref<string>(''); // 流程节点

const showBindingScheme = ref<boolean>(true); // 展示标的方案
const showFee = ref<boolean>(false); // 全单服务费配置
const fullServiceRangeRateLimit = ref<number>(0); // 全单服务费
const fullServiceRemark = ref<string>(''); // 全单服务费
const serviceFeeSets = ref<array>([]); // 全单服务费配置项
const isCateringStandardControl = ref<string>(''); // 是否控制餐标等其他标准配置 - 1:不可修改,2:可以提高,3:可以降低

const isShowDel = ref<boolean>(true); // 展示删除按钮

const pdMainId = ref<number>(null);
const pdVerId = ref<number>(null);

// 缓存查询
const getCache = async () => {
  if (!miceId.value) {
    return;
  }

  cacheLoading.value = true;

  let resCacheStr = '';

  if (schemeType.value === 'reported' || schemeType.value === 'notReported') {
    // 方案互动、方案调整时，才查询缓存

    resCacheStr = await getDataBy({
      applicationCode: 'haierbusiness-mice-merchant',
      cacheKey:
        'haierbusiness-mice-merchant_' +
        loginUser.value?.username +
        '_schemeInteractKey' +
        miceId.value +
        '_merchantId' +
        merchantId.value, // 方案互动
    });
  }

  if (resCacheStr) {
    console.log(
      '%c [ 缓存查询 ]-171114',
      'font-size:13px; background:pink; color:#bf2c9f;',
      resCacheStr ? JSON.parse(resCacheStr) : resCacheStr,
    );

    isSchemeCache.value = true;
    schemeDetail.value = JSON.parse(resCacheStr);
    // 驳回内容反显
    schemeAbandonReason.value = schemeDetail.value?.abandonReason || null;
  }

  cacheLoading.value = false;

  if (
    !resCacheStr &&
    (schemeType.value === 'reported' ||
      schemeType.value === 'notBidding' ||
      schemeType.value === 'schemeView' ||
      schemeType.value === 'biddingView')
  ) {
    // 方案已提报\待竞价
    await getSchemeDetails();
  }

  await getDemandDetails();
};

const getDemandDetails = async () => {
  spinLoading.value = true;

  if (!miceId.value) {
    message.error('查询失败！');
    return;
  }

  let res = {};
  if (['/bidman/present/confirm'].includes(route.path)) {
    res = await miceBidManOrderListApi.userDetails({ miceId: miceId.value });
    schemeType.value = 'schemeView';
  } else {
    if (schemeType.value === 'notBidding' || schemeType.value === 'biddingView') {
      // 竞价
      // 服务商端 - 需求详情
      res = schemeDetail.value;

      // 服务商端 - 需求详情
      const resMer = await schemeApi.demandOrderDetails({
        miceId: miceId.value,
      });

      res.processNode = resMer?.processNode;
      res.mainCode = res.mainCode ? res.mainCode : resMer?.mainCode;

      res.miceName = resMer?.miceName;
      res.personTotal = resMer?.personTotal;
      res.miceType = resMer?.miceType;
      res.startDate = resMer?.startDate;
      res.endDate = resMer?.endDate;
    } else {
      // 服务商端 - 需求详情
      res = await schemeApi.demandOrderDetails({
        miceId: miceId.value,
      });
    }
  }

  // 流程详情
  await getProcessDetails(res.pdMainId || pdMainId.value, res.pdVerId || pdVerId.value, res.pdmMerchantPoolId);

  demandDetail.value = res || {};

  processNode.value = res?.processNode;

  spinLoading.value = false;

  // 方案提报\竞价中 - 展示倒计时
  if (processNode.value === 'SCHEME_SUBMIT') {
    // || processNode.value === 'BIDDING'
    // 1min自动保存
    countDownOneMin();
  }
};

const getSchemeDetails = async () => {
  // 方案详情
  schemeLoading.value = true;

  if (!miceId.value) {
    message.error('查询失败！');
    return;
  }

  // 服务商端 - 方案详情
  const res = await schemeApi.schemePlatDetails({
    miceId: miceId.value,
    miceSchemeId: miceSchemeId.value,
    miceSchemeDemandHotelLockId: miceSchemeDemandHotelLockId.value || null, // 锁定表id
  });

  if (res && res.length > 0) {
    isSchemeCache.value = true;
    schemeDetail.value = res[0];

    if (
      schemeDetail.value.processNode === schemeDetail.value.reverseProcessNode ||
      schemeDetail.value.processNode === schemeDetail.value.reverseAfterProcessNode
    ) {
      // 驳回内容反显
      schemeAbandonReason.value = schemeDetail.value?.abandonReason || null;
    }
  }

  schemeLoading.value = false;
};

// 酒店
const hotelsEmit = (hotelArr: array) => {
  hotelList.value = [...hotelArr];
};
// 日程安排
const schemePlanEmit = (miceSchemeSubData: miceSchemeSubmitRequest) => {
  schemePlanObj.value = { ...miceSchemeSubData };
};
// 布展物料
const schemeMaterialEmit = (materialObj: miceSchemeSubmitRequest) => {
  schemeMaterialObj.value = { ...materialObj };
};
// 礼品
const schemePresentEmit = (presentArr: array) => {
  schemePresentArr.value = [...presentArr];
};
// 其他
const schemeOtherEmit = (otherArr: array) => {
  schemeOtherArr.value = [...otherArr];
};
// 全单服务费
const schemeFeeEmit = (feeObj: miceSchemeSubmitRequest) => {
  schemeFeeObj.value = { ...feeObj };
  schemeTotalInfo.value = { ...schemeTotalInfo.value, ...feeObj };
};
// 附件
const schemeFileEmit = (arr: array) => {
  schemeFileObj.value = [...arr];
};

// 每日计划-方案金额
const planPriceEmit = (priceNum: number) => {
  planPrice.value = priceNum;

  totalPriceFn();
};
// 每日计划-各单项-方案金额
const planEachPriceEmit = (arr: array) => {
  planEachPriceList.value = arr;
};
// 布展物料-方案金额
const materialPriceEmit = (priceNum: number) => {
  materialPrice.value = priceNum;

  totalPriceFn();
};
// 礼品-方案金额
const presentPriceEmit = (priceNum: number) => {
  presentPrice.value = priceNum;

  totalPriceFn();
};
// 其他-方案金额
const otherPriceEmit = (priceNum: number) => {
  otherPrice.value = priceNum;

  totalPriceFn();
};
// 全单服务费方案 - 总金额
const totalPriceFn = debounce(() => {
  totalPrice.value = planPrice.value + materialPrice.value + presentPrice.value + otherPrice.value;

  // 方案暂存 - 合计
  handleTotal();
}, 300);

const handleTotal = debounce(() => {
  // 方案暂存 - 合计
  schemeTemporarily('total');
}, 300);

// 1min自动保存
const countDownOneMin = () => {
  autoSave.value = setInterval(() => {
    if (schemeType.value !== 'notBidding' && schemeType.value !== 'biddingView') {
      // 方案暂存
      schemeTemporarily('auto');
    }
  }, 60000);

  countdownTimer.value = setInterval(() => {
    countdownTime.value = countdownTime.value === 0 ? 60 : countdownTime.value - 1;
  }, 1000);
};

// 缓存删除
const delCache = async () => {
  if (!miceId.value) {
    return;
  }

  delData({
    applicationCode: 'haierbusiness-mice-merchant',
    cacheKey:
      'haierbusiness-mice-merchant_' +
      loginUser.value?.username +
      '_schemeInteractKey' +
      miceId.value +
      '_merchantId' +
      merchantId.value, // 方案互动
  });
};

// 暂存
const schemeTemporarily = async (type) => {
  if (!miceId.value) {
    message.error('暂存失败，会议不存在！');
    return;
  }

  // 日程安排
  schemePlanRef.value && schemePlanRef.value.schemePlanTempSave();

  // 布展物料
  schemeMaterialRef.value && schemeMaterialRef.value.materialTempSave();

  // 礼品
  schemePresentRef.value && schemePresentRef.value.presentTempSave();

  // 其他
  schemeOtherRef.value && schemeOtherRef.value.otherTempSave();

  // 全单服务费
  schemeFeeRef.value && schemeFeeRef.value.serviceFeeTempSave();

  const schemeAllPrice =
    totalPrice.value +
    (schemeFeeObj.value.serviceFee && schemeFeeObj.value.serviceFee.schemeServiceFeeReal
      ? schemeFeeObj.value.serviceFee.schemeServiceFeeReal
      : 0);

  const params = {
    miceId: miceId.value,
    schemeTotalPrice: schemeAllPrice.toFixed(2), // 方案总金额
    // agreementTotalPrice: 0, // 协议总金额
    // marketTotalPrice: 0, // 	市场价总金额
    remarks: demandDetail.value.remarks,

    startDate: demandDetail.value.startDate,
    endDate: demandDetail.value.endDate,

    hotels: [...hotelList.value],
    ...schemePlanObj.value,
    material: { ...schemeMaterialObj.value?.schemeMaterial },
    traffic: {},
    presents: [...schemePresentArr.value],
    others: [...schemeOtherArr.value],
    ...schemeFeeObj.value,
  };

  if (type === 'total') {
    // 合计
    schemeTotalInfo.value = params || {};
    return;
  }

  // 后端缓存
  const res = await saveDataBy({
    applicationCode: 'haierbusiness-mice-merchant',
    // 规则: haierbusiness-mice-bid_工号_你业务的缓存key
    cacheKey:
      'haierbusiness-mice-merchant_' +
      loginUser.value?.username +
      '_schemeInteractKey' +
      miceId.value +
      '_merchantId' +
      merchantId.value, // 方案互动
    cacheValue: JSON.stringify({
      ...params,
    }),
  });

  if (res && type === 'hand') {
    // 手动保存
    message.success('方案互动已暂存！');
  }
};

// 方案作废
const schemeCancel = async () => {
  abandonReason.value = '';
  abandonShow.value = true;
};
const handleAbandon = async () => {
  if (!abandonReason.value) {
    message.error(processNode.value === 'SCHEME_SUBMIT' ? '请输入作废原因' : '请输入放弃原因');
    return;
  }

  let res = {};

  if (processNode.value === 'SCHEME_SUBMIT') {
    const params = {
      miceDemandHotelLockId: hotelLockId.value || miceSchemeDemandHotelLockId.value,
      cancelRemark: abandonReason.value,
    };

    res = await schemeApi.lockHotelCancel({ ...params });
  } else {
    const params = {
      miceId: miceId.value,
      abandonReason: abandonReason.value,
    };

    res = await schemeApi.abstainSchemeBid({ ...params, schemeId: schemeDetail.value.id });
  }

  if (res === null) {
    // 缓存删除
    await delCache();

    message.success(processNode.value === 'SCHEME_SUBMIT' ? '方案已作废！' : '已放弃竞价！');

    if (isCloseLastTab) {
      // 关闭当前页签
      isCloseLastTab.value = true;
    }
    router.push({
      path: '/mice-merchant/scheme/index',
    });
  }

  abandonShow.value = false;
};

// 完成提报
const schemeSub = async () => {
  if (schemePlanRef.value && !schemePlanRef.value.SchemePlanSub()) {
    // 日程安排
    return;
  }

  if (schemeMaterialRef.value && !schemeMaterialRef.value.materialSub()) {
    // 布展物料
    return;
  }

  if (schemePresentRef.value && !schemePresentRef.value.presentSub()) {
    // 礼品
    return;
  }

  if (schemeOtherRef.value && !schemeOtherRef.value.otherSub()) {
    // 其他
    return;
  }

  if (schemeFeeRef.value && !schemeFeeRef.value.serviceFeeSub()) {
    // 全单服务费
    return;
  }

  const schemeAllPrice =
    totalPrice.value +
    (schemeFeeObj.value.serviceFee && schemeFeeObj.value.serviceFee.schemeServiceFeeReal
      ? schemeFeeObj.value.serviceFee.schemeServiceFeeReal
      : 0);
  let params = {
    miceSchemeDemandHotelLockId: hotelLockId.value,
    miceId: miceId.value,
    schemeTotalPrice: schemeAllPrice.toFixed(2), // 方案总金额
    // agreementTotalPrice: 0, // 协议总金额
    // marketTotalPrice: 0, // 	市场价总金额
    remarks: demandDetail.value.remarks,

    hotels: [...hotelList.value],
    ...schemePlanObj.value,
    material: { ...schemeMaterialObj.value?.schemeMaterial },
    traffic: {},
    presents: [...schemePresentArr.value],
    others: [...schemeOtherArr.value],
    ...schemeFeeObj.value,

    abandonReason: schemeAbandonReason.value,
  };

  if (schemeType.value === 'reported') {
    // 方案调整
    params.sourceId = schemeDetail.value.id;

    // 住宿
    params.stays &&
      params.stays.forEach((e) => {
        e.sourceId = e.id;
      });
    // 会场
    params.places &&
      params.places.forEach((e) => {
        e.sourceId = e.id;
      });
    // 用餐
    params.caterings &&
      params.caterings.forEach((e) => {
        e.sourceId = e.id;
      });
    // 用车
    params.vehicles &&
      params.vehicles.forEach((e) => {
        e.sourceId = e.id;
      });
    // 服务人员
    params.attendants &&
      params.attendants.forEach((e) => {
        e.sourceId = e.id;
      });
    // 方案拓展
    params.activities &&
      params.activities.forEach((e) => {
        e.sourceId = e.id;
      });
    // 保险
    params.insurances &&
      params.insurances.forEach((e) => {
        e.sourceId = e.id;
      });

    // 布展物料
    if (params.material && Object.keys(params.material).length > 0) {
      params.material.sourceId = params.material.id;

      params.material &&
        params.material.materialDetails &&
        params.material.materialDetails.forEach((e) => {
          e.sourceId = e.id;
        });
    }
    // 礼品
    params.presents &&
      params.presents.forEach((e) => {
        e.sourceId = e.id;
        e.presentDetails.forEach((e2) => {
          e2.optionType = e.optionType;
          e2.sourceId = e2.id;
        });
      });
    // 其他
    params.others &&
      params.others.forEach((e) => {
        e.sourceId = e.id;
      });

    // 服务费
    if (params.serviceFee && Object.keys(params.serviceFee).length > 0) {
      params.serviceFee.sourceId = schemeDetail.value.id;
    }
  }

  Modal.confirm({
    title: '确定提交？',
    // icon: null,
    content: '',
    onOk: async () => {
      subLoading.value = true;

      const res = await schemeApi.schemeSubmit({ ...params }, (error) => {
        subLoading.value = false;
        errorModal(error?.message);
      });

      subLoading.value = false;

      if (res && res.success) {
        // 缓存删除
        delCache();

        message.success('方案提报成功！');

        if (isCloseLastTab) {
          // 关闭当前页签
          isCloseLastTab.value = true;
        }
        router.push({
          path: '/mice-merchant/scheme/index',
        });
      }
    },
    onCancel() {},
  });
};

// 价格提报
const biddingSub = async () => {
  if (schemePlanRef.value && !schemePlanRef.value.SchemePlanSub()) {
    // 日程安排
    return;
  }

  if (schemeMaterialRef.value && !schemeMaterialRef.value.materialSub()) {
    // 布展物料
    return;
  }

  if (schemePresentRef.value && !schemePresentRef.value.presentSub()) {
    // 礼品
    return;
  }

  if (schemeOtherRef.value && !schemeOtherRef.value.otherSub()) {
    // 其他
    return;
  }

  if (schemeFeeRef.value && !schemeFeeRef.value.serviceFeeSub()) {
    // 全单服务费
    return;
  }

  if (schemeFileRef.value && !schemeFileRef.value.serviceFileSub()) {
    // 附件
    return;
  }

  let stays = [];
  let places = [];
  let caterings = [];
  let vehicles = [];
  let attendants = [];
  let activities = [];
  let insurances = [];

  if (schemePlanObj.value) {
    // 住宿
    if (schemePlanObj.value.stays) {
      stays = schemePlanObj.value.stays.map((e) => {
        return { id: e.id, schemeUnitPrice: e.schemeUnitPrice };
      });
    }

    if (schemePlanObj.value.places) {
      places = schemePlanObj.value.places.map((e) => {
        let totalPrice = e.schemeUnitPlacePrice || 0;

        if (e.hasLed) {
          // 单价*LED数量
          totalPrice += e.schemeUnitLedPrice * e.schemeLedNum;
        }
        if (e.hasTea) {
          // 茶歇单价*会场人数
          totalPrice += e.teaEachTotalPrice * e.schemePersonNum;
        }

        return {
          id: e.id,
          schemeTotalPrice: totalPrice,
          schemeUnitPlacePrice: e.schemeUnitPlacePrice,
          schemeUnitLedPrice: e.schemeUnitLedPrice,
          schemeUnitTeaPrice: e.schemeUnitTeaPrice,
        };
      });
    }
    if (schemePlanObj.value.caterings) {
      caterings = schemePlanObj.value.caterings.map((e) => {
        return { id: e.id, schemeUnitPrice: e.schemeUnitPrice };
      });
    }
    if (schemePlanObj.value.vehicles) {
      vehicles = schemePlanObj.value.vehicles.map((e) => {
        return { id: e.id, schemeUnitPrice: e.schemeUnitPrice };
      });
    }
    if (schemePlanObj.value.attendants) {
      attendants = schemePlanObj.value.attendants.map((e) => {
        return { id: e.id, schemeUnitPrice: e.schemeUnitPrice };
      });
    }
    if (schemePlanObj.value.activities) {
      activities = schemePlanObj.value.activities.map((e) => {
        return { id: e.id, schemeUnitPrice: e.schemeUnitPrice };
      });
    }
    if (schemePlanObj.value.insurances) {
      insurances = schemePlanObj.value.insurances.map((e) => {
        return { id: e.id, schemeUnitPrice: e.schemeUnitPrice };
      });
    }
  }

  let material = {};

  if (
    schemeMaterialObj.value &&
    schemeMaterialObj.value.schemeMaterial &&
    schemeMaterialObj.value.schemeMaterial.materialDetails
  ) {
    material.id = schemeDetail.value?.material?.id;
    material.materialDetails = schemeMaterialObj.value.schemeMaterial.materialDetails.map((e, idx) => {
      return {
        id: schemeDetail.value?.material?.materialDetails[idx].id,
        miceSchemeMaterialId: e.miceSchemeMaterialId,
        schemeUnitPrice: e.schemeUnitPrice,
      };
    });
  }

  let presents = [];
  presents = schemePresentArr.value.map((e, idx) => {
    return {
      id: e.id,
      schemeTotalPrice: e.schemeTotalPrice,
      presentDetails: [
        {
          id: e.id,
          optionType: e.optionType,
          miceSchemePresentDetailsId: e.miceSchemePresentDetailsId,
        },
      ],
    };
  });

  let others = [];
  others = schemeOtherArr.value.map((e, idx) => {
    return {
      id: schemeDetail.value?.others[idx]?.id,
      schemeTotalPrice: e.schemeTotalPrice,
    };
  });

  const schemeServiceFeeRealTwo = schemeFeeObj.value.serviceFee?.schemeServiceFeeReal || 0;

  const serviceFee = {
    id: schemeFeeObj.value.serviceFee?.id || null,
    schemeServiceFeeReal: schemeServiceFeeRealTwo.toFixed(2),
    serviceFeeRate: schemeFeeObj.value.serviceFeeRate,
  };

  const schemeTotalPriceTwo =
    Number(totalPrice.value) + (serviceFee.schemeServiceFeeReal ? Number(serviceFee.schemeServiceFeeReal) : 0) || 0;

  let params = {
    schemeId: schemeDetail.value.id,
    // sourceId: null,
    schemeTotalPrice: schemeTotalPriceTwo.toFixed(2), // 方案总金额
    attachment: [...schemeFileObj.value], // 附件

    stays: [...stays],
    places: [...places],
    caterings: [...caterings],
    vehicles: [...vehicles],
    attendants: [...attendants],
    activities: [...activities],
    insurances: [...insurances],

    material: { ...material },
    // traffic: {},
    presents: [...presents],
    others: [...others],
    serviceFee: { ...serviceFee },
  };

  Modal.confirm({
    title: '确定提交？',
    // icon: null,
    content: '',
    onOk: async () => {
      subLoading.value = true;

      const res = await schemeApi.schemeSubmitBid({ ...params }, (error) => {
        subLoading.value = false;
        errorModal(error?.message);
      });

      subLoading.value = false;

      if (res && res.success) {
        // // 缓存删除
        // delCache();

        message.success('竞价成功！');

        if (isCloseLastTab) {
          // 关闭当前页签
          isCloseLastTab.value = true;
        }
        router.push({
          path: '/mice-merchant/scheme/index',
        });
      }
    },
    onCancel() {},
  });
};

const getUser = async () => {
  // 获取登录服务商的类型
  const res = await schemeApi.getMerchantByUser({});

  // 服务商的类型
  // 1-酒店,2-旅行社,3-保险,4-礼品,5-用车
  merchantType.value = res.merchantType;
  merchantId.value = res.id;
};

// 隐藏标的方案 - TODO
const hideBindingSchemeBtn = () => {
  showBindingScheme.value = !showBindingScheme.value;
};

// 流程详情
const getProcessDetails = async (
  processId = localStorage.getItem('processId') || '',
  verId = '',
  pdmMerchantPoolId = '',
) => {
  // 流程ID
  if (!processId) {
    message.error('流程ID不存在！');
    return;
  }

  processLoading.value = true;

  const res = await miceBidManOrderListApi.processDetails({
    id: processId,
    verId: verId,
  });

  // 需求配置
  const demandProcessList = ProcessOrchestrationServiceTypeEnum.getTypeOptions().map((e) => {
    return e.value;
  });
  const demandSets = numComputedArrMethod(res.items, [...demandProcessList]);

  // 需求配置 - 全单服务费是否配置
  showFee.value = demandSets.includes(2048);

  // 是否控制餐标等其他标准配置 - 1:不可修改,2:可以提高,3:可以降低
  isCateringStandardControl.value = meetingProcessOrchestration(
    'SCHEME_SUBMIT',
    res.nodes || [],
    'schemeSubmitMealLabelConfigDefine',
  );

  if (!showFee.value) {
    // 未设置全单服务费
    fullServiceRangeRateLimit.value = 0;
  } else {
    // 需求配置 - 全单服务费上限
    const feeConfigList = res.merchantPools || [];
    const feeRange = feeConfigList.filter((e) => e.id === pdmMerchantPoolId) || [];

    // 全单服务费配置项
    serviceFeeSets.value = numComputedArrMethod(feeRange[0]?.fullServiceRange - 2048, [...demandProcessList]);

    fullServiceRangeRateLimit.value = feeRange[0]?.fullServiceRangeRateLimit;
    fullServiceRemark.value = feeRange[0]?.fullServiceRemark;
  }

  processLoading.value = false;
};

const delCacheBtn = async () => {
  await delCache();
  window.location.reload();
};

onMounted(async () => {
  const record = resolveParam(route.query.record as string);

  miceId.value = record.miceId;
  miceSchemeId.value = record.miceSchemeId || null;
  schemeType.value = record.schemeType;
  hotelLockId.value = record.hotelLockId;
  miceSchemeDemandHotelLockId.value = record.miceSchemeDemandHotelLockId;
  pdMainId.value = record.pdMainId;
  pdVerId.value = record.pdVerId;

  isShowDel.value = localStorage.getItem('testProcessSignForCiCi') === '1';

  await getUser();

  // 缓存查询
  await getCache();
});

onBeforeUnmount(() => {
  clearInterval(autoSave.value);
  clearInterval(countdownTimer.value);
});
</script>

<template>
  <!-- 方案互动 -->
  <div class="scheme_interact" ref="schemeContainerRef">
    <a-spin
      :spinning="cacheLoading || spinLoading || schemeLoading || subLoading || processLoading"
      tip="Loading..."
      size="large"
    >
      <a-alert
        v-if="schemeAbandonReason"
        class="mb16 demand_reject_reason"
        message="驳回原因："
        :description="schemeAbandonReason"
        show-icon
        type="warning"
      />
      <!-- 顶部 -->
      <schemeInfo class="interact_header" :demandInfo="demandDetail" />

      <div class="interact_content mt20">
        <!-- 标题 -->
        <a-affix :offset-top="0" :target="() => schemeContainerRef">
          <div class="interact_demand_title mb12 pb12">
            <div class="plan_title" v-show="showBindingScheme">
              <img src="@/assets/image/common/demand_icon.png" width="16" />
              <span class="ml12">
                {{ schemeType !== 'notBidding' && schemeType !== 'biddingView' ? '用户需求' : '标的方案' }}
              </span>
            </div>
            <div class="plan_title">
              <img src="@/assets/image/common/plan_icon.png" width="16" />
              <span class="ml12">
                {{ schemeType !== 'notBidding' && schemeType !== 'biddingView' ? '我的方案' : '我的竞价' }}
              </span>
            </div>
          </div>
        </a-affix>

        <!-- 酒店需求 -->
        <div class="interact_hotel common_content p24 mb16" v-if="merchantType === 1 || merchantType === 2">
          <schemeHotel
            :showBindingScheme="showBindingScheme"
            :hotels="demandDetail.hotels"
            :schemeHotels="schemeDetail.hotels"
            :schemeType="schemeType"
            :merchantType="merchantType"
            @hotelsEmit="hotelsEmit"
          />
        </div>
        <!-- 日程安排 -->
        <div class="interact_schedule_plan common_content p24 mb16">
          <schemePlan
            ref="schemePlanRef"
            v-if="
              merchantType !== 4 &&
              !cacheLoading &&
              !spinLoading &&
              !schemeLoading &&
              !subLoading &&
              !processLoading &&
              !['/bidman/present/confirm'].includes(route.path)
            "
            :showBindingScheme="showBindingScheme"
            :schemeContainerRef="schemeContainerRef"
            :processNode="processNode"
            :schemeType="schemeType"
            :demandInfo="demandDetail"
            :schemeCacheInfo="schemeDetail"
            :isSchemeCache="isSchemeCache"
            :hotelList="hotelList"
            :merchantType="merchantType"
            :isCateringStandardControl="isCateringStandardControl"
            @planPriceEmit="planPriceEmit"
            @planEachPriceEmit="planEachPriceEmit"
            @schemePlanEmit="schemePlanEmit"
          />
        </div>
        <!-- 布展物料 -->
        <div
          v-if="
            demandDetail.material &&
            demandDetail.material.materialDetails &&
            demandDetail.material.materialDetails.length > 0 &&
            (merchantType === 1 || merchantType === 2)
          "
          class="interact_wu common_content p24 mb16"
        >
          <scheme-material
            ref="schemeMaterialRef"
            :showBindingScheme="showBindingScheme"
            :schemeType="schemeType"
            :demandInfo="demandDetail"
            :schemeCacheInfo="schemeDetail"
            :isSchemeCache="isSchemeCache"
            @materialPriceEmit="materialPriceEmit"
            @schemeMaterialEmit="schemeMaterialEmit"
          />
        </div>
        <!-- 礼品 -->
        <div
          v-if="
            demandDetail.presents &&
            demandDetail.presents.length > 0 &&
            (merchantType === 4 || ['/bidman/present/confirm'].includes(route.path))
          "
          class="interact_gift common_content p24 mb16"
        >
          <scheme-presents
            ref="schemePresentRef"
            :showBindingScheme="showBindingScheme"
            :schemeType="schemeType"
            :demandInfo="demandDetail"
            :schemeCacheInfo="schemeDetail"
            :isSchemeCache="isSchemeCache"
            @presentPriceEmit="presentPriceEmit"
            @schemePresentEmit="schemePresentEmit"
          />
        </div>
        <!-- 其他 -->
        <div
          v-if="demandDetail.others && demandDetail.others.length > 0 && (merchantType === 1 || merchantType === 2)"
          class="interact_other common_content p24 mb16"
        >
          <scheme-other
            ref="schemeOtherRef"
            :showBindingScheme="showBindingScheme"
            :schemeType="schemeType"
            :demandInfo="demandDetail"
            :schemeCacheInfo="schemeDetail"
            :isSchemeCache="isSchemeCache"
            @otherPriceEmit="otherPriceEmit"
            @schemeOtherEmit="schemeOtherEmit"
          />
        </div>
        <!-- 全单服务费方案 -->
        <div
          class="interact_service_fee common_content p24 mb16"
          v-if="(merchantType === 1 || merchantType === 2) && showFee"
        >
          <scheme-service-fee
            ref="schemeFeeRef"
            :schemeType="schemeType"
            :demandInfo="demandDetail"
            :isSchemeCache="isSchemeCache"
            :materialPrice="materialPrice"
            :presentPrice="presentPrice"
            :otherPrice="otherPrice"
            :planEachPriceList="planEachPriceList"
            :fullServiceRangeRateLimit="fullServiceRangeRateLimit"
            :fullServiceRemark="fullServiceRemark"
            :serviceFeeSets="serviceFeeSets"
            :cacheServiceFee="schemeDetail.serviceFee"
            :schemeCacheInfo="schemeDetail"
            @schemeFeeEmit="schemeFeeEmit"
          />
        </div>
        <!-- 见证性底价资料 -->
        <div
          class="interact_service_file common_content p24 mb16"
          v-if="schemeType === 'notBidding' || schemeType === 'biddingView'"
        >
          <schemeFiles
            ref="schemeFileRef"
            :schemeType="schemeType"
            :schemeCacheInfo="schemeDetail"
            :isSchemeCache="isSchemeCache"
            @schemeFileEmit="schemeFileEmit"
          />
        </div>
        <!-- 合计 -->
        <div class="interact_total_table common_content p24">
          <scheme-total :schemeCacheInfo="schemeTotalInfo" :totalPrice="totalPrice" />
        </div>
      </div>
      <!-- 操作 -->
      <a-affix
        :offset-bottom="0"
        v-if="
          ((processNode === 'SCHEME_SUBMIT' || processNode === 'BIDDING') &&
            schemeType !== 'schemeView' &&
            schemeType !== 'biddingView') ||
          merchantType === 4
        "
      >
        <div class="btns_mar"></div>
        <div class="interact_btns">
          <div class="flex_between">
            <div class="sub_auto_save mr24" v-if="processNode === 'SCHEME_SUBMIT'">
              <div v-show="countdownTime === 0" class="auto_save_img"></div>
              <div class="auto_save_time pl5">
                {{ countdownTime === 0 ? '已自动保存' : countdownTime + 's后自动保存' }}
              </div>
            </div>

            <!-- 方案提报 -->
            <div class="sub_btns" v-if="processNode === 'SCHEME_SUBMIT' || merchantType === 4">
              <a-button v-if="isShowDel" class="mr8" danger @click="delCacheBtn()">缓存删除</a-button>

              <a-button class="mr8" @click="schemeTemporarily('hand')">暂存</a-button>
              <!-- 直签酒店、礼品、保险不允许作废 -->
              <a-button class="mr8" v-if="merchantType === 2" @click="schemeCancel()">方案作废</a-button>
              <a-button type="primary" :loading="subLoading" @click="schemeSub">完成提报</a-button>
            </div>
            <!-- 竞价 -->
            <div class="sub_btns" v-if="processNode === 'BIDDING'">
              <!-- <a-button class="mr8" @click="hideBindingSchemeBtn()">
                {{ (showBindingScheme ? '隐藏' : '显示') + '标的方案' }}
              </a-button> -->

              <!-- <a-button class="mr8" @click="schemeTemporarily('hand')">暂存</a-button> -->
              <a-button v-if="isShowDel" class="mr8" danger @click="delCacheBtn()">缓存删除</a-button>
              <a-button class="mr8" @click="schemeCancel()">放弃竞价</a-button>
              <a-button type="primary" :loading="subLoading" @click="biddingSub()">价格提报</a-button>
            </div>
          </div>
        </div>
      </a-affix>
    </a-spin>

    <!-- 方案作废 - 弹窗 -->
    <a-modal
      v-model:open="abandonShow"
      :title="processNode === 'SCHEME_SUBMIT' ? '方案作废原因' : '竞价放弃原因'"
      width="600px"
      :maskClosable="false"
      @ok="handleAbandon"
    >
      <a-textarea
        style="margin: 24px 0 36px"
        v-model:value="abandonReason"
        :placeholder="processNode === 'SCHEME_SUBMIT' ? '请输入作废原因' : '请输入放弃原因'"
        :maxlength="500"
        showCount
        allow-clear
      />
    </a-modal>
  </div>
</template>

<style>
@import './schemeComponent/schemeInteract.scss';
</style>
<style scoped lang="less">
.scheme_interact {
  height: 100%;
  overflow-y: auto;

  .demand_reject_reason {
    padding: 24px;
    border-radius: 12px;
  }

  .interact_header {
  }

  .interact_content {
  }

  .interact_demand_title {
    display: flex;
    justify-content: space-between;
    background: #f5f5f5;

    .plan_title {
      display: flex;
      justify-content: center;
      align-items: center;

      width: calc(50% - 6px);
      height: 32px;

      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 14px;
      color: #1d2129;
      background: #d5e6ff;
      border-radius: 4px;
      border: 1px solid rgba(24, 104, 219, 0.3);
    }
  }

  .interact_hotel {
  }

  .interact_schedule_plan {
  }

  .interact_wu {
  }

  .interact_gift {
  }

  .interact_other {
  }

  .interact_service_fee {
  }

  .interact_total_table {
  }

  .btns_mar {
    height: 16px;
    background: #f5f5f5;
  }
  .interact_btns {
    width: 100%;

    height: 56px !important;
    line-height: 56px;
    padding: 0 24px;
    background: #ffffff;
    box-shadow: 0px -1px 2px 0px rgba(0, 0, 0, 0.03);
    filter: blur(0px);
    border-top: 1px solid #e8e8e8;

    .flex_between {
      display: flex;
      justify-content: right;
      align-items: center;

      .sub_auto_save {
        display: flex;

        color: #4e5969;
        line-height: 20px;

        .auto_save_img {
          width: 18px;
          height: 18px;
          background: url('@/assets/image/demand/right_green.png');
          background-repeat: no-repeat;
          background-size: 100% 100%;
        }
        .auto_save_time {
          text-align: right;
        }
      }

      .sub_btns {
      }
    }
  }
}
</style>
