<script lang="ts" setup>
import Scheme from '@haierbusiness-front/components/mice/scheme/index.vue';
import SchemeView from '@haierbusiness-front/components/mice/scheme/view.vue';
import { useRoute } from 'vue-router';
const route = useRoute();
</script>

<template>
  <div class="h-full">
    <div class="container">
      <SchemeView v-if="route.path === '/bidman/scheme/confirm/view'" orderSource="user"> </SchemeView>
      <Scheme v-if="route.path === '/bidman/scheme/confirm'" orderSource="user"> </Scheme>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.h-full {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;

  .container {
    width: 1280px;
  }
}
</style>
